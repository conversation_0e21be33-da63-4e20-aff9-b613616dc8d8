import { useState } from 'react'
import SearchForm from '../SearchForm/SearchForm'
import TrustedBy from '../TrustedBy/TrustedBy'
import GeminiButton from '../GeminiButton/GeminiButton'
import { useGeminiAPI } from '../../hooks/useGeminiAPI'
import './HeroText.css'

const HeroText = () => {
  const [headline, setHeadline] = useState('The Ultimate Guide To Use A Unique UI/UX')
  const [description, setDescription] = useState('Lorem ipsum dolor sit amet consectetur. Tincidunt iaculis luctus leo in mattis sagittis facilisis adipiscing.')
  
  const { callGeminiAPI, isLoading } = useGeminiAPI()

  const generateHeadline = async () => {
    const prompt = `Generate one alternative, catchy headline for a guide about UI/UX. The current headline is: "${headline}". Make it concise and powerful.`
    const newHeadline = await callGeminiAPI(prompt)
    if (newHeadline) {
      setHeadline(newHeadline)
    }
  }

  const generateDescription = async () => {
    const prompt = `Based on the headline "${headline}", write a compelling one-sentence description for a UI/UX guide. The current description is: "${description}". Make it engaging and clear.`
    const newDescription = await callGeminiAPI(prompt)
    if (newDescription) {
      setDescription(newDescription)
    }
  }

  return (
    <div className="hero-text">
      <p className="welcome-text">Welcome To BezzDesign</p>
      
      <div className="headline-container">
        <h1 className="hero-headline">{headline}</h1>
        <GeminiButton 
          onClick={generateHeadline}
          disabled={isLoading}
          ariaLabel="Generate new headline with AI"
        />
      </div>

      <div className="description-container">
        <p className="description">{description}</p>
        <GeminiButton 
          onClick={generateDescription}
          disabled={isLoading}
          ariaLabel="Generate new description with AI"
        />
      </div>
      
      <SearchForm />
      <TrustedBy />
    </div>
  )
}

export default HeroText
