/* --- Hero Section Layout --- */
.hero-section {
  position: relative;
  width: 100%;
  min-height: 100vh;
  padding: 2rem 6%;
  display: flex;
  flex-direction: column;
  background: radial-gradient(circle at 10% 20%, rgba(118, 70, 255, 0.15), transparent 35%), 
              radial-gradient(circle at 90% 80%, rgba(165, 148, 253, 0.1), transparent 40%),
              var(--bg-color);
  overflow: hidden;
}

.background-lines {
  position: absolute;
  top: 0; left: 0; width: 100%; height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient id='grad1' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:rgba(255,255,255,0.05);stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:rgba(255,255,255,0);stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Cpath d='M-300,150 C-100,50 100,250 300,150 S 700,50 900,150 S 1300,250 1500,150 S 1900,50 2100,150' stroke='url(%23grad1)' stroke-width='1' fill='none'/%3E%3Cpath d='M-300,300 C-100,200 100,400 300,300 S 700,200 900,300 S 1300,400 1500,300 S 1900,200 2100,300' stroke='url(%23grad1)' stroke-width='1' fill='none'/%3E%3Cpath d='M-300,450 C-100,350 100,550 300,450 S 700,350 900,450 S 1300,550 1500,450 S 1900,350 2100,450' stroke='url(%23grad1)' stroke-width='1' fill='none'/%3E%3Cpath d='M-300,600 C-100,500 100,700 300,600 S 700,500 900,600 S 1300,700 1500,600 S 1900,500 2100,600' stroke='url(%23grad1)' stroke-width='1' fill='none'/%3E%3Cpath d='M-300,750 C-100,650 100,850 300,750 S 700,650 900,750 S 1300,850 1500,750 S 1900,650 2100,750' stroke='url(%23grad1)' stroke-width='1' fill='none'/%3E%3C/svg%3E");
  background-size: cover; opacity: 0.5; z-index: 0; animation: fadeIn 2s ease-in-out;
}

/* --- Main Hero Content --- */
.hero-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-grow: 1;
  gap: 2rem;
  position: relative;
  z-index: 5;
}

/* --- Responsive Design --- */
@media (max-width: 1024px) {
  .hero-section {
    padding: 2rem 4%;
  }
  .hero-content {
    flex-direction: column;
    text-align: center;
    gap: 3rem;
    margin-top: 4rem;
  }
}

@media (max-width: 768px) {
  .hero-content {
    margin-top: 2rem;
  }
}
