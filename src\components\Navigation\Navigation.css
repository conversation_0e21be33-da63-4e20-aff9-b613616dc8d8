/* --- Navigation --- */
nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  z-index: 100;
}

.logo {
  animation: fadeIn 1s ease-out;
}

.nav-links-container {
  display: flex;
  align-items: center;
}

.nav-links {
  display: flex;
  list-style: none;
  gap: 2.5rem;
  animation: fadeIn 1s ease-out 0.2s backwards;
}

.nav-links a {
  text-decoration: none;
  color: var(--text-muted-color);
  font-weight: 500;
  font-size: 1rem;
  transition: color var(--transition-speed);
  position: relative;
  padding-bottom: 5px;
}

.nav-links a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--secondary-color);
  transition: width var(--transition-speed) ease-out;
}

.nav-links a:hover::after,
.nav-links a.active::after {
  width: 100%;
}

.nav-links a:hover,
.nav-links a.active {
  color: white;
}

.nav-buttons {
  display: flex;
  align-items: center;
  gap: 1rem;
  animation: fadeIn 1s ease-out 0.4s backwards;
}

.btn-login {
  text-decoration: none;
  color: var(--text-color);
  font-weight: 500;
  padding: 0.6rem 1.2rem;
  border-radius: 8px;
  transition: background-color var(--transition-speed);
}

.btn-login:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.btn-signup {
  text-decoration: none;
  color: white;
  font-weight: 500;
  padding: 0.6rem 1.2rem;
  border-radius: 8px;
  background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
  transition: transform var(--transition-speed), box-shadow var(--transition-speed);
  display: inline-block;
}

.btn-signup:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 20px rgba(118, 70, 255, 0.4);
}

.btn-signup i {
  margin-left: 0.5rem;
  transition: transform var(--transition-speed);
}

.btn-signup:hover i {
  transform: translateX(3px);
}

.menu-toggle {
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  z-index: 101;
}

/* --- Responsive Design --- */
@media (max-width: 768px) {
  .nav-links-container {
    position: fixed;
    top: 0;
    right: 0;
    width: 70%;
    height: 100vh;
    background: rgba(13, 6, 40, 0.95);
    backdrop-filter: blur(10px);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transform: translateX(100%);
    transition: transform 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  }

  .nav-links-container.active {
    transform: translateX(0);
  }

  .nav-links {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }

  .nav-links a {
    font-size: 1.2rem;
  }

  .nav-buttons {
    flex-direction: column;
    margin-top: 2rem;
    gap: 1.5rem;
  }

  .btn-login,
  .btn-signup {
    width: 150px;
    text-align: center;
  }

  .menu-toggle {
    display: block;
  }
}
