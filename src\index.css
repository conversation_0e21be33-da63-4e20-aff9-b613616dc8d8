/* --- Basic Setup & Variables --- */
:root {
  --bg-color: #0D0628;
  --primary-color: #7646FF;
  --secondary-color: #A594FD;
  --text-color: #E0E0E0;
  --text-muted-color: #BDBDBD;
  --font-family: 'Poppins', sans-serif;
  --transition-speed: 0.3s;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  background-color: var(--bg-color);
  color: var(--text-color);
  overflow-x: hidden; /* Prevents horizontal scroll */
}

/* --- Animations --- */
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.text-update-animation {
  animation: fadeOutIn 0.6s ease-in-out;
}

@keyframes fadeOutIn {
  0% { opacity: 1; transform: translateY(0); }
  50% { opacity: 0; transform: translateY(-10px); }
  100% { opacity: 1; transform: translateY(0); }
}
