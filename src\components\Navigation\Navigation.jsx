import { useState } from 'react'
import './Navigation.css'

const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const navLinks = [
    { href: '#', text: 'Home', active: true },
    { href: '#', text: 'About Us' },
    { href: '#', text: 'Services' },
    { href: '#', text: 'Our Story' },
    { href: '#', text: 'Contact Us' }
  ]

  return (
    <nav>
      <div className="logo">
        <a href="#hero" aria-label="Go to homepage">
          <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 0L27.0711 2.92893L37.0711 12.9289L40 20L37.0711 27.0711L27.0711 37.0711L20 40L12.9289 37.0711L2.92893 27.0711L0 20L2.92893 12.9289L12.9289 2.92893L20 0Z" fill="url(#paint0_linear_1_2)"/>
            <path d="M20 5L24.3301 7.5L32.5 17.5L35 20L32.5 22.5L24.3301 32.5L20 35L15.6699 32.5L7.5 22.5L5 20L7.5 17.5L15.6699 7.5L20 5Z" fill="url(#paint1_linear_1_2)"/>
            <defs>
              <linearGradient id="paint0_linear_1_2" x1="20" y1="0" x2="20" y2="40" gradientUnits="userSpaceOnUse">
                <stop stopColor="#A594FD"/>
                <stop offset="1" stopColor="#7646FF"/>
              </linearGradient>
              <linearGradient id="paint1_linear_1_2" x1="20" y1="5" x2="20" y2="35" gradientUnits="userSpaceOnUse">
                <stop stopColor="white" stopOpacity="0.2"/>
                <stop offset="1" stopColor="white" stopOpacity="0"/>
              </linearGradient>
            </defs>
          </svg>
        </a>
      </div>
      
      <div className={`nav-links-container ${isMenuOpen ? 'active' : ''}`}>
        <ul className="nav-links">
          {navLinks.map((link, index) => (
            <li key={index}>
              <a href={link.href} className={link.active ? 'active' : ''}>
                {link.text}
              </a>
            </li>
          ))}
        </ul>
        <div className="nav-buttons">
          <a href="#" className="btn-login">Log In</a>
          <a href="#" className="btn-signup">
            Sign Up <i className="fa-solid fa-arrow-right"></i>
          </a>
        </div>
      </div>
      
      <button 
        className="menu-toggle" 
        onClick={toggleMenu}
        aria-label="Toggle navigation menu"
      >
        <i className={`fa-solid ${isMenuOpen ? 'fa-times' : 'fa-bars'}`}></i>
      </button>
    </nav>
  )
}

export default Navigation
