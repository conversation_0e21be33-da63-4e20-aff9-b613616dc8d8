import { useRef, useEffect } from 'react'
import Navigation from '../Navigation/Navigation'
import HeroText from './HeroText'
import HeroImage from './HeroImage'
import ScrollButtons from '../ScrollButtons/ScrollButtons'
import './HeroSection.css'

const HeroSection = () => {
  const heroSectionRef = useRef(null)
  const heroImageRef = useRef(null)

  useEffect(() => {
    const heroSection = heroSectionRef.current
    const heroImage = heroImageRef.current

    // Parallax effect for hero image (only on desktop)
    if (window.innerWidth > 1024) {
      const handleMouseMove = (e) => {
        const { clientX, clientY } = e
        const x = (clientX - window.innerWidth / 2) / 25
        const y = (clientY - window.innerHeight / 2) / 25
        if (heroImage) {
          heroImage.style.transform = `rotateY(${-x / 2}deg) rotateX(${y / 2}deg) translateZ(20px)`
        }
      }

      const handleMouseLeave = () => {
        if (heroImage) {
          heroImage.style.transform = 'rotateY(0) rotateX(0) translateZ(0)'
        }
      }

      if (heroSection) {
        heroSection.addEventListener('mousemove', handleMouseMove)
        heroSection.addEventListener('mouseleave', handleMouseLeave)
      }

      return () => {
        if (heroSection) {
          heroSection.removeEventListener('mousemove', handleMouseMove)
          heroSection.removeEventListener('mouseleave', handleMouseLeave)
        }
      }
    }
  }, [])

  return (
    <header className="hero-section" id="hero" ref={heroSectionRef}>
      <div className="background-lines"></div>
      <Navigation />
      <main className="hero-content">
        <HeroText />
        <HeroImage ref={heroImageRef} />
      </main>
      <ScrollButtons />
    </header>
  )
}

export default HeroSection
